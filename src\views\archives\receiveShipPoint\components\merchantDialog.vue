<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      title="选择关联客商"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="main-index">
          <header class="header">
            <div class="header-left">
              <el-input class="w250" v-model="keyword" placeholder="请输入客商编号/名称" clearable></el-input>
            </div>
            <div class="filter-box ml-10 mr-10">
              <el-badge :value="filterNumber" class="filter-badge">
                <el-button type="primary" @click="showFilter = !showFilter">
                  <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                  筛选
                  <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
                </el-button>
              </el-badge>
            </div>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header>
          <FilterContent label-width="140px" v-show="showFilter">
            <InterviewChannel
              :value.sync="filterForm.channelId"
              :record.sync="channelRecord"
              :needCol="true"
            ></InterviewChannel>
            <el-col :span="8">
              <el-form-item label="客商状态">
                <el-select v-model="filterForm.status" placeholder="请选择客商状态" clearable filterable>
                  <el-option
                    v-for="(item, index) in CUSTOMER_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="信用状态">
                <el-select v-model="filterForm.creditStatus" placeholder="请选择信用状态" clearable filterable>
                  <el-option
                    v-for="(item, index) in CREDIT_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="客户类型">
                <el-select v-model="filterForm.type" placeholder="请选择客户类型" clearable filterable>
                  <el-option
                    v-for="(item, index) in CUSTOMER_TYPE"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在地址">
                <el-input v-model="filterForm.address" placeholder="请输入详细地址" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="filterForm.merchantFileCreateTime"
                  type="date"
                  placeholder="请选择创建时间"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="收款员">
                <el-input v-model="filterForm.receivingTeller" placeholder="请输入收款员姓名" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="制单人">
                <el-input v-model="filterForm.single" placeholder="请输入制单人姓名" clearable></el-input>
              </el-form-item>
            </el-col>
          </FilterContent>
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column type="index" width="55" align="center"></el-table-column>
              <el-table-column prop="code" label="客商编号" align="center"> </el-table-column>
              <el-table-column prop="name" label="客商名称" align="center"> </el-table-column>
              <el-table-column prop="type" label="客户类型" align="center">
                <template #default="{ row }">{{ CUSTOMER_TYPE[row.type] }}</template>
              </el-table-column>
              <el-table-column prop="status" label="客商状态" align="center">
                <template #default="{ row }">{{ CUSTOMER_STATUS[row.status] }}</template>
              </el-table-column>
              <el-table-column prop="address" label="详细地址" align="center" min-width="300"></el-table-column>
              <el-table-column prop="creditStatus" label="信用状态" align="center">
                <template #default="{ row }">{{ CREDIT_STATUS[row.creditStatus] }}</template>
              </el-table-column>
              <el-table-column prop="receivingTeller" label="收款员" align="center"></el-table-column>
              <el-table-column prop="single" label="制单人" align="center"></el-table-column>
              <el-table-column prop="merchantFileCreateTime" label="创建时间" align="center"></el-table-column>
              <el-table-column prop="oldCode" label="旧编号" align="center"></el-table-column>
              <el-table-column prop="oldName" label="曾用名" align="center"></el-table-column>
              <el-table-column prop="channelId" label="渠道名称" align="center">
                <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
              </el-table-column>
              <el-table-column min-width="140" label="操作" align="center">
                <template #default="{ row }">
                  <el-link type="danger" @click="handleDelete" v-if="merchantFileId == row.id">删除</el-link>
                  <el-link type="primary" @click="handleAdd(row)" v-else>选择</el-link>
                </template>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { CUSTOMER_TYPE, CREDIT_STATUS, CUSTOMER_STATUS } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      merchantFileId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/merchant/merchantFile/listPage",
        },
        loading: false,
        keyword: "",
        showFilter: false,
        channelRecord: {},
        CUSTOMER_TYPE,
        CREDIT_STATUS,
        CUSTOMER_STATUS,
      };
    },
    methods: {
      async initData() {
        this.loading = true;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          ...this.filterForm,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 重置筛选
      resetFilter() {
        this.keyword = "";
        this.filterForm = {};
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      closeDialog() {
        this.dialogVisible = false;
      },
      handleAdd(row) {
        this.$emit("addMerchant", row);
      },
      handleDelete() {
        this.$emit("deleteMerchant");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w300 {
    width: 300px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
