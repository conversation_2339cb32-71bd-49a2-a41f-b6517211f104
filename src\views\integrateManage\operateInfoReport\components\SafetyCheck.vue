<template>
  <div class="safety-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="自检完成率信息"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen" @change="initData" ref="timeScreen"></TimeScreen>
      </el-col>
    </el-row>
    <main class="safety-main">
      <div class="safety-main-left">
        <div class="chart" id="safety-chart"></div>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-dot green"></span>
            <span class="legend-text">完成率≥90%</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot orange"></span>
            <span class="legend-text">完成率&lt;90%</span>
          </div>
          <div class="legend-item">
            <span class="legend-dot red"></span>
            <span class="legend-text">完成率&lt;80%</span>
          </div>
          <div class="legend-item">
            <span class="legend-line"></span>
            <span class="legend-text">自检正常率</span>
          </div>
        </div>
      </div>
      <div class="safety-main-right">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }" max-height="500">
          <el-table-column prop="type" label="司机类型" align="center">
            <template #default="{ row }">{{ chartData.categories[row.type] || "全体司机" }}</template>
          </el-table-column>
          <el-table-column prop="shouldCount" label="应检次数" align="center"></el-table-column>
          <el-table-column prop="realCount" label="实检次数" align="center"></el-table-column>
          <el-table-column prop="completeRate" label="完成率" align="center">
            <template #default="{ row }">
              <div :style="{ color: getCompleteRateColor(row.completeRate) }">{{ row.completeRate }}%</div>
            </template>
          </el-table-column>
          <el-table-column prop="normalRate" label="正常率" align="center">
            <template #default="{ row }">
              <div>{{ row.normalRate }}%</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    watch: {
      channelId: {
        handler() {
          this.$refs.timeScreen.emitData();
        },
      },
    },
    data() {
      return {
        tableData: [],
        chartInstance: null,
        chartData: {
          categories: ["大型床位司机", "小型床位司机", "小诊所司机"],
          completionRates: [0, 0, 0, 0], // 自检完成率
          normalRates: [0, 0, 0, 0], // 自检正常率
        },
      };
    },
    mounted() {},
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      async initData(params) {
        try {
          let res = await createApiFun(
            { channelId: this.channelId, ...params },
            "/api/operation/info/getVehicleInspectInfo",
          );
          this.chartData.completionRates = res.data.map((item) => item.completeRate);
          this.chartData.normalRates = res.data.map((item) => item.normalRate);
          this.tableData = res.data;
          this.initChart();
        } catch (error) {
          console.log(error);
        }
      },
      initChart() {
        if (!this.chartInstance) {
          const chartDom = document.getElementById("safety-chart");
          this.chartInstance = echarts.init(chartDom);
        }
        const option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
            formatter: function (params) {
              let result = params[0].name + "<br/>";
              params.forEach((param) => {
                if (param.seriesName === "自检完成率") {
                  result += `${param.seriesName}: ${param.value}%<br/>`;
                } else if (param.seriesName === "自检正常率") {
                  result += `${param.seriesName}: ${param.value}%<br/>`;
                }
              });
              return result;
            },
          },
          legend: {
            show: false,
          },
          grid: {
            top: "10%",
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: ["全体司机", ...this.chartData.categories],
              axisPointer: {
                type: "shadow",
              },
              axisLabel: {
                color: "#666",
                fontSize: 12,
                interval: 0,
                rotate: 0,
              },
              axisLine: {
                lineStyle: {
                  color: "#e6e6e6",
                },
              },
              axisTick: {
                show: false,
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "自检完成率(%)",
              min: 0,
              max: 100,
              position: "left",
              axisLabel: {
                formatter: "{value}%",
                color: "#666",
                fontSize: 12,
              },
            },
            {
              type: "value",
              name: "自检正常率(%)",
              min: 0,
              max: 100,
              position: "right",
              axisLabel: {
                formatter: "{value}%",
                color: "#666",
                fontSize: 12,
              },
            },
          ],
          series: [
            {
              name: "自检完成率",
              type: "bar",
              yAxisIndex: 0,
              data: this.chartData.completionRates,
              barWidth: "40%",
              itemStyle: {
                color: function (params) {
                  const value = params.value;
                  if (value >= 90) {
                    return "#10b981"; // 绿色：完成率>=90%
                  }
                  if (value >= 80) {
                    return "#f59e0b"; // 橙色：完成率80-89%
                  }
                  return "#ef4444"; // 红色：完成率<80%
                },
              },
              label: {
                show: true,
                position: "top",
                formatter: "{c}%",
                color: "#666",
                fontSize: 12,
              },
            },
            {
              name: "自检正常率",
              type: "line",
              yAxisIndex: 1,
              data: this.chartData.normalRates,
              lineStyle: {
                color: "#2196F3",
                width: 3,
              },
              itemStyle: {
                color: "#ffffff",
                borderColor: "#2196F3",
                borderWidth: 2,
              },
              symbol: "circle",
              symbolSize: 8,
              label: {
                show: false,
              },
            },
          ],
        };
        this.chartInstance.setOption(option);
      },
      getCompleteRateColor(rate) {
        if (rate >= 90) {
          return "#10b981"; // 绿色：完成率>=90%
        }
        if (rate >= 80) {
          return "#f59e0b"; // 橙色：完成率80-89%
        }
        return "#ef4444"; // 红色：完成率<80%
      },
    },
  };
</script>

<style lang="scss" scoped>
  .safety-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .time-screen {
    justify-content: flex-end;
  }
  .safety-main {
    display: flex;
    .safety-main-left {
      width: 34%;
      height: 500px;
      display: flex;
      flex-direction: column;
      .chart {
        flex: 1;
        width: 100%;
      }
      .chart-legend {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px;
        padding: 10px 0;
        .legend-item {
          display: flex;
          align-items: center;
          gap: 6px;
          .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            &.green {
              background-color: #10b981;
            }
            &.orange {
              background-color: #f59e0b;
            }
            &.red {
              background-color: #ef4444;
            }
          }
          .legend-line {
            width: 20px;
            height: 3px;
            background-color: #2196f3;
            position: relative;
            &::after {
              content: "";
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #ffffff;
              border: 2px solid #2196f3;
            }
          }
          .legend-text {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
    .safety-main-right {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
