<template>
  <div class="comparison-container">
    <el-row :gutter="10">
      <el-col>
        <SubTitle title="各组行驶里程对比"></SubTitle>
      </el-col>
      <el-col>
        <TimeScreen class="mt-16" @change="initData" ref="timeScreen"></TimeScreen>
      </el-col>
    </el-row>
    <div class="chart-box">
      <div class="chart" id="comparison-chart"></div>
    </div>
    <ul class="chart-list">
      <li class="chart-item" v-for="(item, index) in chartData" :key="index">
        <div class="chart-circle" :style="{ backgroundColor: item.color }"></div>
        <div class="chart-name">{{ item.name }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    watch: {
      channelId: {
        handler() {
          this.$refs.timeScreen.emitData();
        },
      },
    },
    data() {
      return {
        chartInstance: null,
        chartData: [
          { name: "大型床位", value: 0, color: "#008000" },
          { name: "小型床位", value: 0, color: "#ffa500" },
          { name: "小诊所组", value: 0, color: "#800080" },
        ],
      };
    },
    mounted() {},
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      async initData(params) {
        try {
          let res = await createApiFun(
            { channelId: this.channelId, ...params },
            "/api/operation/info/getVehicleDailyCompare",
          );
          res.data.forEach((item) => {
            if (item.type || item.type === 0) {
              this.chartData[item.type].value = item.totalMileage;
            }
          });
          this.initChart();
        } catch (error) {
          console.log(error);
        }
      },
      initChart() {
        if (!this.chartInstance) {
          const chartDom = document.getElementById("comparison-chart");
          this.chartInstance = echarts.init(chartDom);
        }
        const option = {
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          legend: {
            show: false,
          },
          xAxis: {
            type: "category",
            data: this.chartData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: "#e0e0e0",
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
              margin: 15,
            },
          },
          yAxis: {
            type: "value",
            name: "里程 (km)",
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            splitLine: {
              lineStyle: {
                color: "#f0f0f0",
                type: "solid",
              },
            },
          },
          series: [
            {
              type: "bar",
              data: this.chartData.map((item) => ({
                name: item.name,
                value: item.value,
                itemStyle: {
                  color: item.color,
                  borderRadius: [4, 4, 0, 0],
                },
              })),
              barWidth: "40%",
              label: {
                show: false,
              },
            },
          ],
          color: this.chartData.map((item) => item.color),
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            formatter: function (params) {
              const data = params[0];
              return `${data.name}<br/>${data.value} km`;
            },
          },
        };
        this.chartInstance.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .comparison-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .chart-box {
    width: 100%;
    height: 400px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
  .chart-list {
    display: flex;
    align-items: center;
    justify-content: center;
    .chart-item {
      display: flex;
      align-items: center;
      margin-left: 20px;
      .chart-circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }
      .chart-name {
        font-size: 14px;
        margin-left: 6px;
      }
    }
  }
</style>
