<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      title="添加关联用户"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="main-index">
          <header class="header">
            <div class="header-left mr-10">
              <el-input class="w300" v-model="keyword" placeholder="请输入用户名称" clearable></el-input>
            </div>
            <el-button @click="initData">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header>
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column prop="fullName" label="真实姓名" align="center"> </el-table-column>
              <el-table-column prop="phone" label="联系电话" align="center"></el-table-column>
              <el-table-column prop="userName" label="用户名" align="center"></el-table-column>
              <el-table-column min-width="140" label="操作" align="center">
                <template #default="{ row }">
                  <el-link type="danger" @click="handleDelete(row)" v-if="selectedIds.includes(row.lgUnionId)"
                    >删除</el-link
                  >
                  <el-link type="primary" @click="handleAdd(row)" v-else>添加</el-link>
                </template>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      pickupPointPhones: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      selectedIds() {
        return this.pickupPointPhones.map((item) => item.lgUnionId);
      },
    },
    data() {
      return {
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/baseuser/listPage",
        },
        loading: false,
        keyword: "",
      };
    },
    methods: {
      initData() {
        this.keyword = "";
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        this.loading = true;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          userIdentity: 1,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas.map((item) => {
              return {
                ...item,
                phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
              };
            });
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      closeDialog() {
        this.dialogVisible = false;
      },
      handleAdd(row) {
        this.$emit("addContact", row);
      },
      handleDelete(row) {
        this.$emit("deleteContact", row);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w300 {
    width: 300px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
