<template>
  <div class="ability-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="司机能力信息"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen" @change="initData" ref="timeScreen"></TimeScreen>
      </el-col>
    </el-row>
    <main class="ability-main">
      <div class="ability-chart">
        <div class="chart" id="ability-chart"></div>
      </div>
      <div class="ability-notice">
        <div class="ability-box">
          <div class="ability-title">能力指标说明</div>
          <ul class="ability-list">
            <li class="ability-item" v-for="item in abilityList" :key="item.id">
              <div class="item-color" :style="{ backgroundColor: item.color }"></div>
              <div class="item-title">{{ item.title }}</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="ability-table">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }" max-height="500">
          <el-table-column prop="driverName" label="司机姓名" align="center"></el-table-column>
          <el-table-column prop="punctualRate" label="完成率" align="center" sortable>
            <template #default="{ row }">
              <div :style="{ color: getCompleteRateColor(row.punctualRate) }">{{ row.punctualRate }}%</div>
            </template>
          </el-table-column>
          <el-table-column prop="selfCheckRate" label="自检率" align="center" sortable>
            <template #default="{ row }">
              <div :style="{ color: getCompleteRateColor(row.selfCheckRate) }">{{ row.selfCheckRate }}%</div>
            </template>
          </el-table-column>
          <el-table-column prop="attendRate" label="出勤率" align="center" sortable>
            <template #default="{ row }">
              <div :style="{ color: getCompleteRateColor(row.attendRate) }">{{ row.attendRate }}%</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    watch: {
      channelId: {
        handler() {
          this.$refs.timeScreen.emitData();
        },
      },
    },
    data() {
      return {
        chartInstance: null,
        tableData: [],
        abilityList: [
          { id: 0, title: "完成率:司机按时完成收运任务的比率", color: "#3b82f6" },
          { id: 2, title: "出勤率:司机按时出勤的比率", color: "#e7a711" },
          { id: 1, title: "安全自检率:司机完成车辆安全自检的比率", color: "#27bc6c" },
        ],
        // 示例数据，您可以根据实际需求修改
        radarData: [],
      };
    },
    mounted() {},
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      async initData(params) {
        try {
          let res = await createApiFun({ channelId: this.channelId, ...params }, "/api/operation/info/getUserRadar");
          this.tableData = res.data;
          this.radarData = res.data.map((item) => {
            return {
              ...item,
              value: [item.punctualRate, item.attendRate, item.selfCheckRate],
            };
          });
          this.initChart();
        } catch (error) {
          console.log(error);
        }
      },
      initChart() {
        if (!this.chartInstance) {
          const chartDom = document.getElementById("ability-chart");
          this.chartInstance = echarts.init(chartDom);
        }
        const option = {
          radar: {
            // 设置雷达图为三角形
            shape: "polygon",
            center: ["50%", "60%"], // 向上移动中心点
            radius: "100%", // 进一步增大半径
            startAngle: 90, // 从顶部开始
            splitNumber: 5,
            indicator: [
              { name: "完成率", max: 100 },
              { name: "出勤率", max: 100 },
              { name: "安全自检率", max: 100 },
            ],
            axisName: {
              fontSize: 14,
              color: "#666",
              fontWeight: "bold",
              distance: 15, // 增加标签与雷达图的距离
            },
            splitLine: {
              lineStyle: {
                color: "#e0e6ed",
                width: 1,
              },
            },
            splitArea: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: "#e0e6ed",
                width: 1,
              },
            },
          },
          series: [
            {
              name: "司机能力",
              type: "radar",
              data: this.radarData.map((item) => {
                // 找到value数组中最大值的索引
                const maxIndex = item.value.indexOf(Math.max(...item.value));
                const selectedColor = this.abilityList[maxIndex].color;

                return {
                  value: item.value,
                  name: item.driverName,
                  lineStyle: {
                    color: selectedColor,
                    width: 2,
                  },
                  areaStyle: {
                    color: selectedColor,
                    opacity: 0.1,
                  },
                  symbol: "circle",
                  symbolSize: 6,
                  itemStyle: {
                    color: selectedColor,
                    borderColor: "#fff",
                    borderWidth: 2,
                  },
                };
              }),
            },
          ],
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              const data = params.data;
              return `
                <div style="padding: 8px;">
                  <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
                  <div>完成率: ${data.value[0]}%</div>
                  <div>出勤率: ${data.value[1]}%</div>
                  <div>安全自检率: ${data.value[2]}%</div>
                </div>
              `;
            },
          },
        };
        this.chartInstance.setOption(option);
      },
      getCompleteRateColor(rate) {
        if (rate >= 90) {
          return "#10b981"; // 绿色：完成率>=90%
        }
        if (rate >= 80) {
          return "#f59e0b"; // 橙色：完成率80-89%
        }
        return "#ef4444"; // 红色：完成率<80%
      },
    },
  };
</script>

<style lang="scss" scoped>
  .ability-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .time-screen {
    justify-content: flex-end;
  }
  .ability-main {
    display: flex;
    justify-content: space-between;
    .ability-chart {
      flex: 1;
      overflow: hidden;
      .chart {
        width: 100%;
        height: 500px;
      }
    }
    .ability-table {
      flex: 1;
      overflow: hidden;
    }
    .ability-notice {
      display: flex;
      align-items: flex-end;
      margin-bottom: 50px;
      margin-left: 16px;
      margin-right: 50px;
      .ability-box {
        background-color: #f9fafb;
        border-radius: 10px;
        padding: 16px;
        padding-right: 40px;
        .ability-title {
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }
  .ability-list {
    .ability-item {
      display: flex;
      align-items: center;
      margin-top: 6px;
      .item-color {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        overflow: hidden;
      }
      .item-title {
        font-size: 14px;
        color: #333;
        margin-left: 10px;
      }
    }
  }
</style>
