<template>
  <div class="anomaly-container">
    <SubTitle title="当日司机行驶里程异常提醒"></SubTitle>
    <ul class="anomaly-list" v-if="anomalyList.length > 0">
      <el-row
        type="flex"
        justify="space-between"
        align="middle"
        class="anomaly-item"
        :class="{ active: item.abnormalType === 1 }"
        v-for="(item, index) in anomalyList"
        :key="index"
      >
        <el-col :span="1" class="item-color"></el-col>
        <el-col :span="5" class="item-name">{{ item.driverName }}</el-col>
        <el-col :span="5" class="item-title"
          >当日平均行驶里程较所在车队{{ item.abnormalType === 1 ? "偏高" : "偏低" }}</el-col
        >
        <el-col :span="3" class="item-name">{{ item.abnormalType === 1 ? "+" : "-" }}{{ item.percent }}%</el-col>
        <el-col :span="5" class="item-text">当日行驶里程{{ item.driverMileage }}km</el-col>
        <el-col :span="5" class="item-text"
          >当日{{ typeList[item.type] }}司机平均行驶里程{{ item.avgMileage }}km</el-col
        >
      </el-row>
    </ul>
    <el-empty v-else :image-size="150" description="暂无异常"></el-empty>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import { createApiFun } from "@/api/base";
  import moment from "moment";
  export default {
    components: {
      SubTitle,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    watch: {
      channelId: {
        handler() {
          this.initData();
        },
      },
    },
    data() {
      return {
        anomalyList: [],
        typeList: ["大型床位", "小型床位", "小诊所"],
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        try {
          let res = await createApiFun(
            { channelId: this.channelId, day: moment().format("YYYY-MM-DD") },
            "/api/operation/info/getVehicleDailyError",
          );
          this.anomalyList = res.data;
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .anomaly-container {
    margin-top: 16px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .anomaly-list {
    height: 180px;
    overflow-y: auto;
  }
  .anomaly-item {
    background-color: #e3f8f1;
    margin-top: 16px;
    padding-right: 40px;
    .item-color {
      width: 10px;
      height: 44px;
      background-color: #10b981;
      filter: blur(1px) brightness(110%);
    }
    .item-name {
      font-size: 28px;
      color: #10b981;
    }
    &.active {
      background-color: #fef2f2;
      .item-color {
        background-color: #ef4444;
      }
      .item-name {
        color: #ef4444;
      }
    }
  }
</style>
