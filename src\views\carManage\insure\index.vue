<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record
        v-if="showRecord"
        :recordId="recordId"
        :plateNumber="plateNumber"
        :year="year"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入车牌号/经办人/保单号" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="import-dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button class="mr-10" size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
            <el-popover placement="bottom" width="240" trigger="click" :append-to-body="false">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="所属年份">
              <el-date-picker
                v-model="filterForm.year"
                type="year"
                placeholder="请选择所属年份"
                value-format="yyyy"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保单开始日期范围">
              <el-date-picker
                v-model="filterForm.policyEffectiveDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保单结束日期范围">
              <el-date-picker
                v-model="filterForm.policyTerminationDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            ref="tableRef"
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
          >
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column
              prop="plateNumber"
              label="车牌号"
              align="center"
              v-if="itemList[0].value"
            ></el-table-column>
            <el-table-column prop="year" label="所属年份" align="center" v-if="itemList[1].value"></el-table-column>
            <el-table-column label="交强险" align="center">
              <el-table-column prop="policyNo" label="保单号" align="center" v-if="itemList[2].value">
                <template #default="{ row }">{{ row.policyNo || "-" }}</template>
              </el-table-column>
              <el-table-column prop="operatorName" label="经办人" align="center" v-if="itemList[3].value">
                <template #default="{ row }">{{ row.operatorName || "-" }}</template>
              </el-table-column>
              <el-table-column prop="money" label="保单金额（元）" align="center" v-if="itemList[4].value">
                <template #default="{ row }">{{ row.money || "-" }}</template>
              </el-table-column>
              <el-table-column prop="insuranceCompany" label="保险公司" align="center" v-if="itemList[5].value">
                <template #default="{ row }">{{ row.insuranceCompany || "-" }}</template>
              </el-table-column>
              <el-table-column prop="policyEffectiveDate" label="保单开始日期" align="center" v-if="itemList[6].value">
                <template #default="{ row }">{{ row.policyEffectiveDate || "-" }}</template>
              </el-table-column>
              <el-table-column
                prop="policyTerminationDate"
                label="保单终止日期"
                align="center"
                v-if="itemList[7].value"
              >
                <template #default="{ row }">{{ row.policyTerminationDate || "-" }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="商业险" align="center">
              <el-table-column prop="syPolicyNo" label="保单号" align="center" v-if="itemList[2].value">
                <template #default="{ row }">{{ row.syPolicyNo || "-" }}</template>
              </el-table-column>
              <el-table-column prop="syOperatorName" label="经办人" align="center" v-if="itemList[3].value">
                <template #default="{ row }">{{ row.syOperatorName || "-" }}</template>
              </el-table-column>
              <el-table-column prop="syMoney" label="保单金额（元）" align="center" v-if="itemList[4].value">
                <template #default="{ row }">{{ row.syMoney || "-" }}</template>
              </el-table-column>
              <el-table-column prop="syInsuranceCompany" label="保险公司" align="center" v-if="itemList[5].value">
                <template #default="{ row }">{{ row.syInsuranceCompany || "-" }}</template>
              </el-table-column>
              <el-table-column
                prop="syPolicyEffectiveDate"
                label="保单开始日期"
                align="center"
                v-if="itemList[6].value"
              >
                <template #default="{ row }">{{ row.syPolicyEffectiveDate || "-" }}</template>
              </el-table-column>
              <el-table-column
                prop="syPolicyTerminationDate"
                label="保单终止日期"
                align="center"
                v-if="itemList[7].value"
              >
                <template #default="{ row }">{{ row.syPolicyTerminationDate || "-" }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="承运险" align="center">
              <el-table-column prop="cyPolicyNo" label="保单号" align="center" v-if="itemList[2].value">
                <template #default="{ row }">{{ row.cyPolicyNo || "-" }}</template>
              </el-table-column>
              <el-table-column prop="cyOperatorName" label="经办人" align="center" v-if="itemList[3].value">
                <template #default="{ row }">{{ row.cyOperatorName || "-" }}</template>
              </el-table-column>
              <el-table-column prop="cyMoney" label="保单金额（元）" align="center" v-if="itemList[4].value">
                <template #default="{ row }">{{ row.cyMoney || "-" }}</template>
              </el-table-column>
              <el-table-column prop="cyInsuranceCompany" label="保险公司" align="center" v-if="itemList[5].value">
                <template #default="{ row }">{{ row.cyInsuranceCompany || "-" }}</template>
              </el-table-column>
              <el-table-column
                prop="cyPolicyEffectiveDate"
                label="保单开始日期"
                align="center"
                v-if="itemList[6].value"
              >
                <template #default="{ row }">{{ row.cyPolicyEffectiveDate || "-" }}</template>
              </el-table-column>
              <el-table-column
                prop="cyPolicyTerminationDate"
                label="保单终止日期"
                align="center"
                v-if="itemList[7].value"
              >
                <template #default="{ row }">{{ row.cyPolicyTerminationDate || "-" }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="80" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-popconfirm title="确认删除当前投保记录？" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="车辆投保记录"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import { INSURANCE_TYPE } from "@/enums";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import importDialog from "@/components/importDialog";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      importDialog,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicleInsure/listPage",
          delete: "/api/vehicleInsure/delete/",
          export: "/api/vehicleInsure/exportExcelVehicleInsure",
          import: "/api/vehicleInsure/import",
          template: "/api/vehicleInsure/template",
        },
        showRecord: false,
        recordId: "",
        INSURANCE_TYPE,
        insuranceOptions: [],
        plateNumber: "",
        year: "",
        showFilter: false,
        keyword: "",
        itemList: [
          { label: "车牌号", value: true },
          { label: "所属年份", value: true },
          { label: "保单号", value: true },
          { label: "经办人", value: true },
          { label: "保单金额（元）", value: false },
          { label: "保险公司", value: false },
          { label: "保单开始日期", value: false },
          { label: "保单终止日期", value: true },
        ],
        allItemChecked: false,
        channelRecord: {},
        importDialogShow: false,
        importDialogType: "",
        loading: false,
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          policyEffectiveBeginDate: this.filterForm.policyEffectiveDate ? this.filterForm.policyEffectiveDate[0] : "",
          policyEffectiveEndDate: this.filterForm.policyEffectiveDate ? this.filterForm.policyEffectiveDate[1] : "",
          policyTerminationBeginDate: this.filterForm.policyTerminationDate
            ? this.filterForm.policyTerminationDate[0]
            : "",
          policyTerminationEndDate: this.filterForm.policyTerminationDate
            ? this.filterForm.policyTerminationDate[1]
            : "",
          year: this.filterForm.year,
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
        this.plateNumber = "";
        this.year = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
        this.plateNumber = row.plateNumber;
        this.year = row.year;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                keyword: this.keyword,
                policyEffectiveBeginDate: this.filterForm.policyEffectiveDate
                  ? this.filterForm.policyEffectiveDate[0]
                  : "",
                policyEffectiveEndDate: this.filterForm.policyEffectiveDate
                  ? this.filterForm.policyEffectiveDate[1]
                  : "",
                policyTerminationBeginDate: this.filterForm.policyTerminationDate
                  ? this.filterForm.policyTerminationDate[0]
                  : "",
                policyTerminationEndDate: this.filterForm.policyTerminationDate
                  ? this.filterForm.policyTerminationDate[1]
                  : "",
                year: this.filterForm.year,
                channelId: this.filterForm.channelId || "",
              });
              if (res.success) {
                createDownloadEvent(`车辆投保记录${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
