<template>
  <div class="rubbish-box">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="今日垃圾收运量"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen" @change="initData" ref="timeScreen"></TimeScreen>
      </el-col>
    </el-row>
    <div class="rubbish-grid-box">
      <div class="rubbish-content mb-16">
        <div class="rubbish-content-title">收运垃圾总重量</div>
        <div class="rubbish-content-box">
          <div class="rubbish-count">
            <div class="count-big">{{ formData?.total }}</div>
            <div class="count-unit">（kg）</div>
          </div>
        </div>
      </div>
      <div>
        <el-row :gutter="16">
          <el-col :lg="12" :xl="8" v-for="(item, index) in rubbishList" :key="index">
            <div class="rubbish-content mb-16">
              <div class="rubbish-content-title">{{ item.name }}</div>
              <div class="rubbish-content-box">
                <div class="rubbish-count">
                  <div class="count-small">{{ formData[item.key] }}</div>
                  <div class="count-unit">（kg）</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    watch: {
      channelId: {
        handler() {
          this.$refs.timeScreen.emitData();
        },
      },
    },
    data() {
      return {
        rubbishList: [
          { name: "感染性废物重量", key: "infectiousWaste" },
          { name: "损伤性废物重量", key: "damaginWaste" },
          { name: "药物性废物重量", key: "pharmaceuticalWaste" },
          { name: "病理性废物重量", key: "pathologicalWaste" },
          { name: "化学性废物重量", key: "chemicalWaste" },
          { name: "感染性——污泥重量", key: "sludge" },
        ],
        formData: {
          infectiousWaste: 0, //感染性废物
          damaginWaste: 0, //损伤性废物
          pharmaceuticalWaste: 0, //药物性废物
          pathologicalWaste: 0, //病理性废物
          chemicalWaste: 0, //化学性废物
          sludge: 0, //感染性污泥
        },
      };
    },
    methods: {
      async initData(params) {
        try {
          let res = await createApiFun({ channelId: this.channelId, ...params }, "/api/operation/info/getWaybillInfo");
          this.formData = res.data;
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .rubbish-box {
    padding: 16px;
    padding-bottom: 0;
    background-color: #fff;
    border: 1px solid #d7d7d7;
    border-radius: 10px;
    .rubbish-grid-box {
      display: grid;
      grid-gap: 16px;
      grid-template-columns: repeat(2, 1fr);
      .rubbish-content {
        padding: 16px;
        border: 1px solid #d7d7d7;
        border-radius: 6px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        .rubbish-content-title {
          font-size: 14px;
          color: #333;
        }
        .rubbish-content-box {
          flex: 1;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 16px;
        }
        .rubbish-count {
          display: flex;
          align-items: flex-end;
          .count-big {
            font-size: 50px;
            font-weight: bold;
            line-height: 38px;
          }
          .count-small {
            font-size: 28px;
            line-height: 24px;
          }
          .count-unit {
            font-size: 12px;
          }
        }
      }
    }
  }
  .time-screen {
    justify-content: flex-end;
  }
</style>
