<template>
  <div class="average-container">
    <SubTitle title="平均行驶里程统计"></SubTitle>
    <el-row>
      <el-col class="mt-10">
        <el-select v-model="typeValue" placeholder="请选择司机类型" class="w200" @change="handleChange">
          <el-option
            v-for="item in typeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
            clearable
            filterable
          ></el-option>
        </el-select>
      </el-col>
      <el-col class="mt-10">
        <TimeScreen @change="initData" ref="timeScreen"></TimeScreen>
      </el-col>
    </el-row>
    <div class="chart-box">
      <div class="chart" id="average-chart"></div>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    watch: {
      channelId: {
        handler() {
          this.$refs.timeScreen.emitData();
        },
      },
    },
    data() {
      return {
        typeList: [
          {
            id: null,
            name: "全体司机",
          },
          {
            id: 0,
            name: "大型床位司机",
          },
          {
            id: 1,
            name: "小型床位司机",
          },
          {
            id: 2,
            name: "小诊所司机",
          },
        ],
        typeValue: null,
        chartInstance: null,
        timeType: 0,
        quarterList: ["第一季度", "第二季度", "第三季度", "第四季度"],
        dataList: [],
      };
    },
    mounted() {},
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      handleChange() {
        this.$refs.timeScreen.emitData();
      },
      async initData(params) {
        try {
          let res = await createApiFun(
            { channelId: this.channelId, type: this.typeValue, ...params },
            "/api/operation/info/getVehicleDailyAvg",
          );
          this.dataList = res.data;
          this.timeType = params.timeType || 0;
          this.initChart();
        } catch (error) {
          console.log(error);
        }
      },
      // 处理数据
      dealWithData() {
        let xAxisData = [];
        let seriesData = [];
        if (this.dataList.length > 0) {
          seriesData = this.dataList.map((item) => item.totalMileage);
          switch (this.timeType) {
            case 0:
              xAxisData = this.dataList.map((item) => item.date);
              break;
            case 1:
              xAxisData = this.dataList.map(
                (item) => `${item.year}-${item.month >= 10 ? item.month : "0" + item.month}`,
              );
              break;
            case 2:
              xAxisData = this.dataList.map((item) => `${item.year}-${this.quarterList[item.quarter]}`);
              break;
            case 3:
              xAxisData = this.dataList.map((item) => `${item.year}`);
              break;
          }
        }
        return {
          xAxisData,
          seriesData,
        };
      },
      initChart() {
        if (!this.chartInstance) {
          const chartDom = document.getElementById("average-chart");
          this.chartInstance = echarts.init(chartDom);
        }
        let { xAxisData, seriesData } = this.dealWithData();
        const option = {
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: true,
            data: xAxisData,
            axisLine: {
              lineStyle: {
                color: "#e6e6e6",
              },
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
              interval: 0, // 强制显示所有标签
              rotate: 0, // 标签旋转角度，如果标签过长可以设置为45
              margin: 8, // 标签与轴线的距离
            },
            axisTick: {
              show: true,
              length: 5,
              lineStyle: {
                color: "#e6e6e6",
              },
            },
          },
          yAxis: {
            type: "value",
            name: "里程 (km)",
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            splitLine: {
              lineStyle: {
                color: "#f0f0f0",
                type: "solid",
              },
            },
          },
          series: [
            {
              type: "line",
              data: seriesData,
              smooth: true,
              symbol: "circle",
              symbolSize: 6,
              lineStyle: {
                color: "#003366",
                width: 2,
              },
              itemStyle: {
                color: "#003366",
              },
              areaStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(0, 51, 102, 0.3)",
                    },
                    {
                      offset: 1,
                      color: "rgba(0, 51, 102, 0.05)",
                    },
                  ],
                },
              },
            },
          ],
          tooltip: {
            trigger: "axis",
            formatter: function (params) {
              const data = params[0];
              return `${data.name}<br/>里程: ${data.value} km`;
            },
          },
        };

        this.chartInstance.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .w200 {
    width: 200px;
  }
  .mt-12 {
    margin-top: 10px;
  }
  .average-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .chart-box {
    width: 100%;
    height: 320px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
